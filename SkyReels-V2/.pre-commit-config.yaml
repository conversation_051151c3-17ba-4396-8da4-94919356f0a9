repos:
  - repo: https://github.com/asottile/reorder-python-imports.git
    rev: v3.8.3
    hooks:
      - id: reorder-python-imports
        name: Reorder Python imports
        types: [file, python]
  - repo: https://github.com/psf/black.git
    rev: 22.8.0
    hooks:
      - id: black
        additional_dependencies: ['click==8.0.4']
        args: [--line-length=120]
        types: [file, python]
  - repo: https://github.com/pre-commit/pre-commit-hooks.git
    rev: v4.3.0
    hooks:
      - id: check-byte-order-marker
        types: [file, python]
      - id: trailing-whitespace
        types: [file, python]
      - id: end-of-file-fixer
        types: [file, python]

    